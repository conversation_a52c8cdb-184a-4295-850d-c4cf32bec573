"use client";

import {
  Table,
  TableHeader,
  TableColumn,
  TableBody,
  TableRow,
  TableCell,
  Button,
  Input,
  Modal,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalFooter,
  useDisclosure,
} from "@heroui/react";
import Papa from "papaparse";
import { getDocs, query, deleteDoc, doc, getDoc } from "firebase/firestore";
import { collection, addDoc } from "firebase/firestore";
import { db } from "@/firebase";

import { useEffect, useState, useCallback } from "react";
import { getAuth } from "firebase/auth";
import { useDiseaseData } from "../../../contexts/DiseaseDataContext";
import NotificationModal from "../../../components/NotificationModal";

interface CsvData {
  [key: string]: string | number;
}

export default function Body() {
  const [ArrayKeys, setArrayKeys] = useState<string[]>([]);
  const [ArrayValues, setArrayValues] = useState<CsvData[]>([]);
  const [loading, setLoading] = useState(false);
  const [deleteLoading, setDeleteLoading] = useState(false);
  const [tableLoading, setTableLoading] = useState(true); // Loading state for table data
  const [uploadProgress, setUploadProgress] = useState<{
    current: number;
    total: number;
    message: string;
  }>({ current: 0, total: 0, message: "" });

  // Notification modal state para sa success/error messages
  const [notification, setNotification] = useState<{
    isOpen: boolean;
    type: 'success' | 'error' | 'warning' | 'info';
    title: string;
    message: string;
    details: string[];
  }>({
    isOpen: false,
    type: 'info',
    title: '',
    message: '',
    details: []
  });

  const { isOpen, onOpen, onOpenChange } = useDisclosure();
  const { refreshData } = useDiseaseData();

  const [userMunicipality, setUserMunicipality] = useState<string>("");
  const [validationError, setValidationError] = useState<{
    show: boolean;
    title: string;
    message: string;
    invalidMunicipalities: string[];
  }>({
    show: false,
    title: "",
    message: "",
    invalidMunicipalities: []
  });

  const auth = getAuth();
  const user = auth.currentUser;

  // Helper function para mag show ng notification modal instead of alert
  const showNotification = (
    type: 'success' | 'error' | 'warning' | 'info',
    title: string,
    message: string,
    details: string[] = []
  ) => {
    setNotification({
      isOpen: true,
      type,
      title,
      message,
      details
    });
  };

  // Close notification modal
  const closeNotification = () => {
    setNotification(prev => ({ ...prev, isOpen: false }));
  };


  const fetchUserMunicipality = useCallback(async (): Promise<string | null> => {
    if (!user) return null;

    try {
      const userDocRef = doc(db, 'healthradarDB', 'users', 'healthworker', user.uid);
      const userDoc = await getDoc(userDocRef);

      if (userDoc.exists()) {
        const userData = userDoc.data();
        const municipality = userData.municipality;
        console.log(`User's assigned municipality: ${municipality}`);
        return municipality;
      } else {
        console.error('User document not found');
        return null;
      }
    } catch (error) {
      console.error('Error fetching user municipality:', error);
      return null;
    }
  }, [user]);
  // Municipality validation function - users can only upload data for their assigned municipality
  // This ensures data integrity and proper access control
  const validateMunicipalityData = (csvData: CsvData[], userMunicipality: string): {
    isValid: boolean;
    invalidMunicipalities: string[];
    invalidRecords: CsvData[];
    validRecords: CsvData[];
  } => {
    const invalidMunicipalities = new Set<string>();
    const invalidRecords: CsvData[] = [];
    const validRecords: CsvData[] = [];

    console.log(`🔍 Validating CSV data for user municipality: ${userMunicipality}`);

    csvData.forEach((record, index) => {
      const recordMunicipality = record.Municipality?.toString().trim();

      if (!recordMunicipality) {
        console.warn(`⚠️ Record ${index + 1}: Missing municipality data`);
        invalidRecords.push(record);
        return;
      }

      if (recordMunicipality.toLowerCase() !== userMunicipality.toLowerCase()) {
        console.warn(`❌ Record ${index + 1}: Municipality mismatch - Found: "${recordMunicipality}", Expected: "${userMunicipality}"`);
        invalidMunicipalities.add(recordMunicipality);
        invalidRecords.push(record);
      } else {
        console.log(`✅ Record ${index + 1}: Valid municipality - "${recordMunicipality}"`);
        validRecords.push(record);
      }
    });

    const isValid = invalidMunicipalities.size === 0;
    console.log(`📊 Validation Summary: ${validRecords.length} valid, ${invalidRecords.length} invalid records`);

    return {
      isValid,
      invalidMunicipalities: Array.from(invalidMunicipalities),
      invalidRecords,
      validRecords
    };
  };

  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    setLoading(true);
    const file = event.target.files?.[0];
    if (!file) {
      setLoading(false);
      return;
    }

    // Use Papa Parse para ma parse ang CSV - mas reliable ni kaysa manual parsing
    Papa.parse(file, {
      header: true,
      skipEmptyLines: true,
      complete: async (results) => {
        console.log("Parsed Results:", results.data);
        const parsedData = results.data as CsvData[];

        if (parsedData.length === 0) {
          setValidationError({
            show: true,
            title: "Empty CSV File",
            message: "The uploaded CSV file is empty or contains no valid data.",
            invalidMunicipalities: []
          });
          setLoading(false);
          return;
        }

        // Enhanced user logging para makita kung kinsa nag login ug asa siya gikan
        console.log("=".repeat(60));
        console.log("🔐 USER AUTHENTICATION & MUNICIPALITY CHECK");
        console.log("=".repeat(60));
        console.log(`👤 Current User: ${user?.email}`);
        console.log(`🆔 User UID: ${user?.uid}`);

        if (!user) {
          console.error("❌ User not authenticated.");
          setLoading(false);
          return;
        }

        const userMunicipality = await fetchUserMunicipality();
        if (!userMunicipality) {
          console.error("❌ Could not determine user's municipality");
          showNotification(
            'error',
            'Municipality Not Found ❌',
            'Could not determine your assigned municipality. Please contact support.',
            [
              'Your user profile may be incomplete',
              'Contact your administrator',
              'Ensure your account is properly set up',
              'Try logging out and logging back in'
            ]
          );
          setLoading(false);
          return;
        }

        setUserMunicipality(userMunicipality);

        // Enhanced municipality logging
        console.log(`🏘️ User's Assigned Municipality: ${userMunicipality.toUpperCase()}`);
        console.log(`📊 CSV Records to Process: ${parsedData.length}`);
        console.log(`🔍 Starting municipality validation...`);

        // Municipality validation - users can only upload data for their assigned municipality
        const validation = validateMunicipalityData(parsedData, userMunicipality);

        if (!validation.isValid) {
          console.error("❌ MUNICIPALITY VALIDATION FAILED");
          console.error(`Invalid municipalities found: ${validation.invalidMunicipalities.join(', ')}`);
          console.error(`Valid records: ${validation.validRecords.length}`);
          console.error(`Invalid records: ${validation.invalidRecords.length}`);

          showNotification(
            'error',
            'Municipality Validation Failed ❌',
            `You can only upload data for your assigned municipality: ${userMunicipality}. The uploaded file contains data for other municipalities.`,
            [
              `Your municipality: ${userMunicipality}`,
              `Invalid municipalities found: ${validation.invalidMunicipalities.join(', ')}`,
              `Valid records: ${validation.validRecords.length}`,
              `Invalid records: ${validation.invalidRecords.length}`,
              'Please remove data from other municipalities and try again'
            ]
          );
          setLoading(false);
          return;
        }

        // Validation passed
        console.log("✅ MUNICIPALITY VALIDATION PASSED");
        console.log(`All ${parsedData.length} records belong to municipality: ${userMunicipality}`);
        console.log(`🚀 Proceeding with upload to global database...`);

        if (parsedData.length > 0) {
          const keys = Object.keys(parsedData[0]);
          setArrayKeys(keys);
          setArrayValues(parsedData);
        }

        try {
          console.log("Starting global upload process for", parsedData.length, "records");

          // Initialize progress tracking
          setUploadProgress({
            current: 0,
            total: parsedData.length,
            message: "Starting upload..."
          });

          // Upload in smaller batches to avoid overwhelming Firebase
          // Batch size of 10 para hindi ma overwhelm ang Firebase
          const batchSize = 10;
          let successCount = 0;
          let errorCount = 0;
          const errors: string[] = [];

          for (let i = 0; i < parsedData.length; i += batchSize) {
            const batch = parsedData.slice(i, i + batchSize);
            const batchNumber = Math.floor(i / batchSize) + 1;
            const totalBatches = Math.ceil(parsedData.length / batchSize);

            console.log(`Uploading batch ${batchNumber}/${totalBatches} (${batch.length} records)`);

            // Update progress
            setUploadProgress({
              current: i,
              total: parsedData.length,
              message: `Uploading batch ${batchNumber}/${totalBatches}...`
            });

            try {
              // Upload current batch
              await Promise.all(
                batch.map(async (disease, index) => {
                  try {
                    await addDoc(
                      collection(
                        db,
                        "healthradarDB",
                        "centralizedData",
                        "allCases"
                      ),
                      {
                        ...disease,
                        uploadedBy: user.uid,
                        uploadedByEmail: user.email,
                        uploadedByMunicipality: userMunicipality,
                        uploadedAt: new Date().toISOString(),
                        batchNumber: batchNumber,
                        recordIndex: i + index
                      }
                    );
                    successCount++;
                  } catch (recordError) {
                    errorCount++;
                    const errorMsg = `Record ${i + index + 1}: ${recordError}`;
                    errors.push(errorMsg);
                    console.error(errorMsg);
                  }
                })
              );

              // Update progress after batch completion
              setUploadProgress({
                current: Math.min(i + batchSize, parsedData.length),
                total: parsedData.length,
                message: `Completed batch ${batchNumber}/${totalBatches}`
              });

              // Small delay between batches para hindi ma overwhelm ang Firebase
              if (i + batchSize < parsedData.length) {
                await new Promise(resolve => setTimeout(resolve, 500));
              }

            } catch (batchError) {
              console.error(`Error uploading batch ${batchNumber}:`, batchError);
              errorCount += batch.length;
              errors.push(`Batch ${batchNumber}: ${batchError}`);
            }
          }

          console.log(`Upload completed! Success: ${successCount}, Errors: ${errorCount}`);

          // Update final progress
          setUploadProgress({
            current: parsedData.length,
            total: parsedData.length,
            message: errorCount > 0 ? `Completed with ${errorCount} errors` : "Upload completed successfully!"
          });

          if (errorCount > 0) {
            console.error("Upload errors:", errors);
            showNotification(
              'warning',
              'Upload Completed with Errors',
              `Upload completed with some issues. ${successCount} records were successfully uploaded, but ${errorCount} records failed.`,
              [
                `✅ Successful uploads: ${successCount} records`,
                `❌ Failed uploads: ${errorCount} records`,
                'Check browser console for detailed error information',
                'You may need to re-upload the failed records'
              ]
            );
          } else {
            console.log("✅ ALL DISEASES UPLOADED SUCCESSFULLY!");
            console.log(`Municipality: ${userMunicipality}`);
            console.log(`Records uploaded: ${successCount}`);
            console.log(`User: ${user.email}`);

            showNotification(
              'success',
              'Upload Successful! 🎉',
              `Congratulations! All ${successCount} disease records from ${userMunicipality} have been successfully uploaded to the global database.`,
              [
                `🏘️ Municipality: ${userMunicipality}`,
                `📊 Total records uploaded: ${successCount}`,
                `👤 Uploaded by: ${user.email}`,
                '🌍 Data is now available globally to all users',
                '📈 Charts and analytics have been updated'
              ]
            );
          }

          // Wait a bit before refreshing to ensure Firebase has processed all writes
          setUploadProgress({
            current: parsedData.length,
            total: parsedData.length,
            message: "Processing data..."
          });

          console.log("Waiting for Firebase to process all writes...");
          await new Promise(resolve => setTimeout(resolve, 2000));

          // Refresh both the data context (for charts) and table data
          setUploadProgress({
            current: parsedData.length,
            total: parsedData.length,
            message: "Refreshing display..."
          });

          console.log("Refreshing data...");
          await refreshData();
          await fetchUploadedCases();

          // Verify that data was actually saved by checking the count
          setUploadProgress({
            current: parsedData.length,
            total: parsedData.length,
            message: "Verifying saved data..."
          });

          // Wait a bit more and verify the data was saved
          await new Promise(resolve => setTimeout(resolve, 1000));

          try {
            const verificationRef = collection(db, "healthradarDB", "centralizedData", "allCases");
            const verificationSnapshot = await getDocs(query(verificationRef));
            const savedCount = verificationSnapshot.docs.length;

            console.log(`Verification: Found ${savedCount} total records in database`);

            if (savedCount >= successCount) {
              console.log("✅ Data verification successful!");
              setUploadProgress({
                current: parsedData.length,
                total: parsedData.length,
                message: `✅ Upload verified! ${savedCount} total records in database`
              });
            } else {
              console.warn(`⚠️ Verification warning: Expected at least ${successCount} records, found ${savedCount}`);
              setUploadProgress({
                current: parsedData.length,
                total: parsedData.length,
                message: `⚠️ Verification: ${savedCount} records found (expected ${successCount})`
              });
            }
          } catch (verificationError) {
            console.error("Error during verification:", verificationError);
            setUploadProgress({
              current: parsedData.length,
              total: parsedData.length,
              message: "Upload completed (verification failed)"
            });
          }

        } catch (error) {
          console.error("Critical error during upload process:", error);
          showNotification(
            'error',
            'Upload Failed ❌',
            'A critical error occurred during the upload process. Please check your connection and try again.',
            [
              `Error details: ${error}`,
              'Check your internet connection',
              'Verify your CSV file format is correct',
              'Try uploading a smaller batch of records',
              'Contact support if the problem persists'
            ]
          );
          setUploadProgress({
            current: 0,
            total: 0,
            message: "Upload failed"
          });
        } finally {
          setLoading(false);
          // Clear progress after a short delay
          setTimeout(() => {
            setUploadProgress({ current: 0, total: 0, message: "" });
          }, 3000);
        }
      },
    });
  };


  // Fetch global centralized data instead of user-specific data
  // Para makita tanan users ang same data regardless of kung kinsa nag login
  const fetchUploadedCases = useCallback(async () => {
    if (!user) {
      setTableLoading(false);
      return;
    }

    setTableLoading(true);
    try {
      console.log("Fetching global centralized data for disease management table...");

      // Fetch from centralized collection that contains ALL municipality data
      const centralizedCasesRef = collection(
        db,
        "healthradarDB",
        "centralizedData",
        "allCases"
      );

      const querySnapshot = await getDocs(query(centralizedCasesRef));
      console.log(`Found ${querySnapshot.docs.length} records in centralized collection for table display`);

      const fetchedData: CsvData[] = [];
      querySnapshot.forEach((doc) => {
        const data = doc.data();
        // Remove metadata fields that were added during upload
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        const { uploadedBy, uploadedByEmail, uploadedAt, ...cleanData } = data as CsvData & {
          uploadedBy?: string;
          uploadedByEmail?: string;
          uploadedAt?: string;
        };
        fetchedData.push(cleanData);
      });

      if (fetchedData.length > 0) {
        // Get all unique keys from all documents para complete ang table headers
        const allKeys = [
          ...new Set(fetchedData.flatMap((doc) => Object.keys(doc))),
        ];
        setArrayKeys(allKeys);
        setArrayValues(fetchedData);

        // Store in localStorage as backup para dili mawala when navigating
        try {
          localStorage.setItem('diseaseManagementKeys', JSON.stringify(allKeys));
          localStorage.setItem('diseaseManagementData', JSON.stringify(fetchedData));
        } catch (error) {
          console.warn("Could not save to localStorage:", error);
        }

        console.log(`Successfully loaded ${fetchedData.length} global records for table display`);
      } else {
        // Clear table if no data
        setArrayKeys([]);
        setArrayValues([]);

        // Clear localStorage as well
        try {
          localStorage.removeItem('diseaseManagementKeys');
          localStorage.removeItem('diseaseManagementData');
        } catch (error) {
          console.warn("Could not clear localStorage:", error);
        }

        console.log("No global data found, table will be empty");
      }
    } catch (error) {
      console.error("Error fetching global centralized data:", error);

      // Fallback: try to load from localStorage
      try {
        const savedKeys = localStorage.getItem('diseaseManagementKeys');
        const savedData = localStorage.getItem('diseaseManagementData');

        if (savedKeys && savedData) {
          const keys = JSON.parse(savedKeys);
          const data = JSON.parse(savedData);
          setArrayKeys(keys);
          setArrayValues(data);
          console.log(`Loaded ${data.length} records from localStorage backup`);
        } else {
          // No backup data available
          setArrayKeys([]);
          setArrayValues([]);
          console.log("No backup data available, table will be empty");
        }
      } catch (localStorageError) {
        console.error("Error loading from localStorage:", localStorageError);
        setArrayKeys([]);
        setArrayValues([]);
      }
    } finally {
      setTableLoading(false);
    }
  }, [user]);

  // Delete ALL disease data from ALL users (global clear)
  const deleteAllGlobalCases = useCallback(async () => {
    if (!user) {
      console.log("❌ No user authenticated - kinsa man ni wala'y login?");
      return;
    }

    // Get user municipality for logging
    const userMunicipality = await fetchUserMunicipality();

    // Enhanced logging for delete operation
    console.log("=".repeat(60));
    console.log("🗑️ GLOBAL DELETE OPERATION INITIATED");
    console.log("=".repeat(60));
    console.log(`👤 User: ${user.email}`);
    console.log(`🏘️ User Municipality: ${userMunicipality || 'Unknown'}`);
    console.log(`🆔 User UID: ${user.uid}`);
    console.log(`⏰ Timestamp: ${new Date().toISOString()}`);

    // Confirm deletion since this is a destructive operation
    const confirmDelete = window.confirm(
      "Are you sure you want to delete ALL disease data from ALL users? This action cannot be undone!"
    );

    if (!confirmDelete) {
      console.log("❌ Delete operation cancelled by user");
      return;
    }

    setDeleteLoading(true);
    try {
      console.log("Starting global deletion of all disease data...");

      // Delete ALL records from centralized collection (global data)
      const centralizedRef = collection(
        db,
        "healthradarDB",
        "centralizedData",
        "allCases"
      );

      const centralizedSnapshot = await getDocs(query(centralizedRef));
      console.log(`Found ${centralizedSnapshot.docs.length} records in centralized collection`);

      // Delete tanan documents sa centralized collection
      const deleteCentralizedPromises = centralizedSnapshot.docs.map((document) =>
        deleteDoc(doc(db, "healthradarDB", "centralizedData", "allCases", document.id))
      );

      await Promise.all(deleteCentralizedPromises);

      // Also clear current user's personal collection for consistency
      const casesRef = collection(
        db,
        "healthradarDB",
        "users",
        "healthworker",
        user.uid,
        "UploadedCases"
      );

      const querySnapshot = await getDocs(query(casesRef));
      console.log(`Found ${querySnapshot.docs.length} records in user's personal collection`);

      // Delete tanan documents sa personal user collection
      const deletePersonalPromises = querySnapshot.docs.map((document) =>
        deleteDoc(doc(db, "healthradarDB", "users", "healthworker", user.uid, "UploadedCases", document.id))
      );

      await Promise.all(deletePersonalPromises);

      const totalDeleted = centralizedSnapshot.docs.length;
      console.log(`Successfully deleted ${totalDeleted} records from global database`);

      // Clear local state
      setArrayKeys([]);
      setArrayValues([]);

      // Refresh data context para ma update ang charts
      await refreshData();

      showNotification(
        'success',
        'Global Data Cleared Successfully! 🗑️',
        `All ${totalDeleted} disease records have been permanently deleted from the global database.`,
        [
          `🗑️ Total records deleted: ${totalDeleted}`,
          '🌍 All users will now see empty data',
          '📊 Charts and analytics have been reset',
          '✅ Database is ready for fresh data upload'
        ]
      );
    } catch (error) {
      console.error("Error deleting global cases:", error);
      showNotification(
        'error',
        'Delete Operation Failed ❌',
        'An error occurred while trying to delete the global data. Please try again.',
        [
          `Error details: ${error}`,
          'Check your internet connection',
          'Verify you have proper permissions',
          'Try refreshing the page and attempting again',
          'Contact support if the problem persists'
        ]
      );
    } finally {
      setDeleteLoading(false);
    }
  }, [user, refreshData, fetchUserMunicipality]);


  // Load data when component mounts and when user changes
  useEffect(() => {
    console.log("Disease management useEffect triggered", {
      hasUser: !!user,
      userEmail: user?.email
    });

    if (user) {
      console.log("Disease management component mounted, fetching data...");
      fetchUploadedCases();
    } else {
      console.log("No user authenticated, clearing table data");
      setArrayKeys([]);
      setArrayValues([]);
      setTableLoading(false);
    }
  }, [user, fetchUploadedCases]);

  // Additional effect to monitor data changes
  useEffect(() => {
    console.log("Table data changed:", {
      keysLength: ArrayKeys.length,
      valuesLength: ArrayValues.length,
      keys: ArrayKeys,
      sampleData: ArrayValues.slice(0, 2) // Show first 2 records for debugging
    });
  }, [ArrayKeys, ArrayValues]);

  return (
    <div className="flex-1 overflow-auto">
      {}
      <div className="bg-white shadow-sm border-b border-gray-200 p-6">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold text-[#143D60] mb-2">Disease Management</h1>
            <p className="text-gray-600">Upload, manage, and analyze disease case data</p>
          </div>
          <div className="flex items-center gap-4">
            <div className="flex items-center gap-2 text-sm text-gray-600">
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
              <span>{ArrayValues.length} records</span>
            </div>
          </div>
        </div>
      </div>

      {}
      <div className="p-6">
        {}
        <div className="bg-white rounded-2xl shadow-lg p-6 border border-gray-100 mb-6">
          <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
            <div className="flex-1 max-w-md">
              <Input
                placeholder="Search disease name..."
                className="w-full"
                classNames={{
                  input: "text-gray-900 placeholder:text-gray-400",
                  inputWrapper: "border-gray-200 hover:border-[#A0C878] focus-within:border-[#A0C878] bg-gray-50 hover:bg-white transition-colors"
                }}
                startContent={
                  <svg className="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                  </svg>
                }
                size="lg"
                radius="lg"
              />
            </div>

            <div className="flex gap-3">
              <Button
                onPress={onOpen}
                disabled={loading}
                className="bg-gradient-to-r from-[#143D60] to-[#1e4a6b] text-white font-semibold shadow-lg hover:shadow-xl transition-all duration-300"
                startContent={
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                  </svg>
                }
                size="lg"
                radius="lg"
              >
                Upload CSV
              </Button>

              <Button
                onPress={deleteAllGlobalCases}
                disabled={deleteLoading}
                className="bg-gradient-to-r from-red-500 to-red-600 text-white font-semibold shadow-lg hover:shadow-xl transition-all duration-300"
                startContent={
                  deleteLoading ? (
                    <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                  ) : (
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                    </svg>
                  )
                }
                size="lg"
                radius="lg"
              >
                {deleteLoading ? "Deleting..." : "Clear All Global Data"}
              </Button>
            </div>
          </div>
        </div>

        {/* Upload Progress Indicator */}
        {uploadProgress.total > 0 && (
          <div className="bg-white rounded-2xl shadow-lg border border-gray-100 p-6 mb-6">
            <div className="flex items-center gap-4">
              <div className="w-8 h-8 border-2 border-[#143D60] border-t-transparent rounded-full animate-spin"></div>
              <div className="flex-1">
                <div className="flex justify-between items-center mb-2">
                  <span className="text-sm font-semibold text-[#143D60]">{uploadProgress.message}</span>
                  <span className="text-sm text-gray-600">
                    {uploadProgress.current}/{uploadProgress.total} records
                  </span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div
                    className="bg-gradient-to-r from-[#143D60] to-[#1e4a6b] h-2 rounded-full transition-all duration-300"
                    style={{
                      width: `${uploadProgress.total > 0 ? (uploadProgress.current / uploadProgress.total) * 100 : 0}%`
                    }}
                  ></div>
                </div>
              </div>
            </div>
          </div>
        )}

        {}
        <div className="bg-white rounded-2xl shadow-lg border border-gray-100">
          <div className="p-6 border-b border-gray-200">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-gradient-to-r from-[#A0C878] to-[#DDEB9D] rounded-lg flex items-center justify-center">
                <svg className="w-5 h-5 text-[#143D60]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 17V7m0 10a2 2 0 01-2 2H5a2 2 0 01-2-2V7a2 2 0 012-2h2a2 2 0 012 2m0 10a2 2 0 002 2h2a2 2 0 002-2M9 7a2 2 0 012-2h2a2 2 0 012 2m0 10V7m0 10a2 2 0 002 2h2a2 2 0 002-2V7a2 2 0 00-2-2H9a2 2 0 00-2 2v10z" />
                </svg>
              </div>
              <div>
                <h3 className="text-xl font-bold text-[#143D60]">Disease Case Data</h3>
                <p className="text-sm text-gray-600">Uploaded CSV data overview</p>
              </div>
            </div>
          </div>

          <div className="p-6">
            {tableLoading ? (
              <div className="text-center py-12">
                <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <div className="w-8 h-8 border-2 border-[#143D60] border-t-transparent rounded-full animate-spin"></div>
                </div>
                <h3 className="text-lg font-semibold text-gray-700 mb-2">Loading Data...</h3>
                <p className="text-gray-500">Fetching global disease management data</p>
              </div>
            ) : ArrayValues.length > 0 ? (
              <div className="overflow-x-auto">
                <Table
                  aria-label="Disease case data table"
                  classNames={{
                    wrapper: "shadow-none border border-gray-200 rounded-xl",
                    th: "bg-gray-50 text-[#143D60] font-semibold",
                    td: "text-gray-700"
                  }}
                >
                  <TableHeader>
                    {ArrayKeys.map((key: string) => (
                      <TableColumn key={key} className="text-center">
                        {key}
                      </TableColumn>
                    ))}
                  </TableHeader>
                  <TableBody>
                    {ArrayValues.map((row: CsvData, index: number) => (
                      <TableRow key={index} className="hover:bg-gray-50">
                        {ArrayKeys.map((key: string, idx: number) => (
                          <TableCell key={idx} className="text-center">
                            {row[key] || ""}
                          </TableCell>
                        ))}
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            ) : (
              <div className="text-center py-12">
                <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                </div>
                <h3 className="text-lg font-semibold text-gray-700 mb-2">No Data Available</h3>
                <p className="text-gray-500 mb-4">Upload a CSV file to view global disease case data</p>
                <Button
                  onPress={onOpen}
                  className="bg-gradient-to-r from-[#143D60] to-[#1e4a6b] text-white font-semibold"
                  startContent={
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                    </svg>
                  }
                >
                  Upload CSV File
                </Button>
              </div>
            )}
          </div>
        </div>

        {    
         }
        <Modal isOpen={isOpen} onOpenChange={onOpenChange} size="lg">
          <ModalContent>
            {(onClose) => (
              <>
                <ModalHeader className="text-xl font-bold text-[#143D60] border-b border-gray-200 pb-4">
                  <div className="flex items-center gap-3">
                    <div className="w-8 h-8 bg-gradient-to-r from-[#143D60] to-[#1e4a6b] rounded-lg flex items-center justify-center">
                      <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                      </svg>
                    </div>
                    Upload CSV File
                  </div>
                </ModalHeader>
                <ModalBody className="py-6">
                  <div className="space-y-4">
                    <div className="text-center">
                      <p className="text-gray-600 mb-4">Select a CSV file containing disease case data</p>
                    </div>
                    <Input
                      type="file"
                      accept=".csv"
                      onChange={(e) => {
                        handleFileUpload(e);
                        onClose();
                      }}
                      className="w-full"
                      classNames={{
                        input: "text-gray-900",
                        inputWrapper: "border-2 border-dashed border-gray-300 hover:border-[#A0C878] bg-gray-50 hover:bg-white transition-colors h-20"
                      }}
                      size="lg"
                      radius="lg"
                    />
                    <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                      <h4 className="text-sm font-semibold text-blue-800 mb-2">CSV Format Requirements:</h4>
                      <ul className="text-xs text-blue-700 space-y-1">
                        <li>• Include columns: Municipality, DiseaseName, CaseCount</li>
                        <li>• Use proper municipality names (e.g., &quot;Lilo-an&quot;, &quot;Mandaue&quot;, &quot;Consolacion&quot;)</li>
                        <li>• Ensure CaseCount contains numeric values</li>
                        <li>• <strong>Important:</strong> You can only upload data for your assigned municipality</li>
                      </ul>
                    </div>
                  </div>
                </ModalBody>
                <ModalFooter className="border-t border-gray-200 pt-4">
                  <Button
                    color="danger"
                    variant="light"
                    onPress={onClose}
                    className="font-semibold"
                  >
                    Cancel
                  </Button>
                </ModalFooter>
              </>
            )}
          </ModalContent>
        </Modal>

        {/* erroring validation sa pag submit csv */}
        <Modal
          isOpen={validationError.show}
          onOpenChange={(open) => setValidationError(prev => ({ ...prev, show: open }))}
          size="lg"
        >
          <ModalContent>
            {(onClose) => (
              <>
                <ModalHeader className="text-xl font-bold text-red-600 border-b border-gray-200 pb-4">
                  <div className="flex items-center gap-3">
                    <div className="w-8 h-8 bg-red-100 rounded-lg flex items-center justify-center">
                      <svg className="w-5 h-5 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                      </svg>
                    </div>
                    {validationError.title}
                  </div>
                </ModalHeader>
                <ModalBody className="py-6">
                  <div className="space-y-4">
                    <p className="text-gray-700">
                      {validationError.message}
                    </p>

                    {validationError.invalidMunicipalities.length > 0 && (
                      <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                        <h4 className="text-sm font-semibold text-red-800 mb-2">
                          Invalid municipalities found in your CSV:
                        </h4>
                        <ul className="text-sm text-red-700 space-y-1">
                          {validationError.invalidMunicipalities.map((municipality, index) => (
                            <li key={index} className="flex items-center gap-2">
                              <span className="w-2 h-2 bg-red-500 rounded-full"></span>
                              {municipality}
                            </li>
                          ))}
                        </ul>
                      </div>
                    )}

                    <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                      <h4 className="text-sm font-semibold text-blue-800 mb-2">
                         What you need to do:
                      </h4>                      <ul className="text-sm text-blue-700 space-y-1">
                        <li>• Ensure all data in your CSV belongs to your assigned municipality</li>
                        <li>• Check the &quot;Municipality&quot; column in your CSV file</li>
                        <li>• Remove or correct any entries from other municipalities</li>
                        <li>• Upload only data for your municipality: <strong>{userMunicipality}</strong></li>
                      </ul>
                    </div>
                  </div>
                </ModalBody>
                <ModalFooter className="border-t border-gray-200 pt-4">
                  <Button
                    color="primary"
                    onPress={onClose}
                    className="bg-gradient-to-r from-[#143D60] to-[#1e4a6b] text-white font-semibold"
                  >
                    I Understand
                  </Button>
                </ModalFooter>
              </>
            )}
          </ModalContent>
        </Modal>

        {/* Notification Modal para sa success/error messages */}
        <NotificationModal
          isOpen={notification.isOpen}
          onClose={closeNotification}
          type={notification.type}
          title={notification.title}
          message={notification.message}
          details={notification.details}
          autoClose={notification.type === 'success'}
          autoCloseDelay={6000}
        />
      </div>
    </div>
  );
}
